import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_demo/core/config/injection/injection.dart';
import 'package:flutter_demo/core/router/app_navigator.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_bloc.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_event.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_state.dart';
import 'package:flutter_demo/features/splash/presentation/widgets/user_agreement_dialog.dart';

/// 启动页面
///
/// 显示应用启动画面，处理初始化逻辑
class SplashScreen extends StatelessWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SplashBloc>()..add(InitializeAppEvent()),
      child: const SplashView(),
    );
  }
}

/// 启动页视图
///
/// 启动页面的内容部分
class SplashView extends StatefulWidget {
  const SplashView({Key? key}) : super(key: key);

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  @override
  Widget build(BuildContext context) {
    // 监听状态变化
    return BlocListener<SplashBloc, SplashState>(
      listener: (context, state) {
        Logger.info('SplashScreen', '收到状态变化：${state.runtimeType}');

        if (state is ShowUserAgreementState) {
          // 显示用户协议对话框
          Logger.info('SplashScreen', '显示用户协议对话框');
          _showUserAgreementDialog(context);
        } else if (state is UserAgreementRejectedState) {
          // 用户拒绝协议，退出应用
          Logger.info('SplashScreen', '用户拒绝协议，退出应用');
          _exitApp(context);
        } else if (state is ShowGuideState) {
          // 首次启动，显示引导页
          Logger.info('SplashScreen', '跳转到引导页');
          AppNavigator.goToGuide(context);
        } else if (state is AuthenticatedState) {
          // 已登录，导航到首页
          Logger.info('SplashScreen', '跳转到首页');
          AppNavigator.goToHome(context);
        } else if (state is UnauthenticatedState) {
          // 未登录，导航到登录页面
          Logger.info('SplashScreen', '跳转到登录页');
          AppNavigator.goToLogin(context);
        } else if (state is InitializationFailureState) {
          Logger.info('splash','应用初始化失败：${state.message}');
          AppNavigator.goToLogin(context);
        }
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/splash_bg.png'),
              fit: BoxFit.fill,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示用户协议对话框
  void _showUserAgreementDialog(BuildContext context) {
    // 保存原始 context，用于访问 SplashBloc
    final originalContext = context;

    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext dialogContext) {
        return UserAgreementDialog(
          onAccept: () {
            Logger.info('SplashScreen', '用户点击同意协议按钮');
            Navigator.of(dialogContext).pop();
            // 发送同意协议事件 - 使用原始 context 访问 SplashBloc
            Logger.info('SplashScreen', '发送 AcceptUserAgreementEvent 事件');
            originalContext.read<SplashBloc>().add(AcceptUserAgreementEvent());
          },
          onReject: () {
            _exitApp(dialogContext);
          },
        );
      },
    );
  }

  /// 退出应用
  void _exitApp(BuildContext context) {
    // 直接退出应用
    if (Platform.isAndroid) {
      // 使用 exit 强制退出
      exit(0);
    } else {
      // iOS 显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请手动关闭应用')),
      );
    }
  }
}
