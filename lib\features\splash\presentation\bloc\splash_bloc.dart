import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/auth/domain/usecases/get_current_user_usecase.dart';
import 'package:flutter_demo/features/guide/domain/usecases/check_first_launch_usecase.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_event.dart';
import 'package:flutter_demo/features/splash/presentation/bloc/splash_state.dart';

/// 启动页 BLoC
///
/// 处理启动页相关的业务逻辑
class SplashBloc extends Bloc<SplashEvent, SplashState> {
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final LocalStorage _localStorage;
  final CheckFirstLaunchUseCase _checkFirstLaunchUseCase;

  SplashBloc({
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required LocalStorage localStorage,
    required CheckFirstLaunchUseCase checkFirstLaunchUseCase,
  })  : _getCurrentUserUseCase = getCurrentUserUseCase,
        _localStorage = localStorage,
        _checkFirstLaunchUseCase = checkFirstLaunchUseCase,
        super(SplashInitialState()) {
    on<InitializeAppEvent>(_onInitializeApp);
    on<AcceptUserAgreementEvent>(_onAcceptUserAgreement);
    on<RejectUserAgreementEvent>(_onRejectUserAgreement);
  }

  /// 处理初始化应用事件
  /// 1. 显示启动页
  /// 2. 检查用户协议同意状态
  /// 3. 如果未同意协议，显示协议对话框
  /// 4. 如果已同意协议，检查是否首次启动
  /// 5. 根据启动状态和认证状态导航到相应页面
  Future<void> _onInitializeApp(
    InitializeAppEvent event,
    Emitter<SplashState> emit,
  ) async {
    emit(AppInitializingState());

    try {
      // 模拟初始化过程（2 秒）
      // await Future.delayed(const Duration(seconds: 1));

      // 首先检查用户是否已同意协议
      final hasAcceptedAgreement = _localStorage.getBool(AppConstants.userAgreementAcceptedKey) ?? false;
      Logger.debug('SplashBloc', 'hasAcceptedAgreement: $hasAcceptedAgreement');

      if (!hasAcceptedAgreement) {
        // 用户未同意协议，显示协议对话框
        emit(ShowUserAgreementState());
        return;
      }

      // 用户已同意协议，继续原有逻辑
      await _continueAfterAgreement(emit);
    } on Exception catch (e) {
      Logger.error('SplashBloc', '应用初始化失败：$e');
      emit(InitializationFailureState(e.toString()));
    }
  }

  /// 用户同意协议后继续启动流程
  Future<void> _continueAfterAgreement(Emitter<SplashState> emit) async {
    try {
      Logger.info('SplashBloc', '开始检查首次启动状态');

      // 临时简化：直接跳转到登录页进行测试
      Logger.info('SplashBloc', '临时测试：直接跳转到登录页');
      emit(UnauthenticatedState());
      return;

      // 检查是否首次启动
      final isFirstLaunch = await _checkFirstLaunchUseCase();
      Logger.info('SplashBloc', 'isFirstLaunch: $isFirstLaunch');

      if (isFirstLaunch) {
        // 首次启动，显示引导页
        Logger.info('SplashBloc', '首次启动，发出 ShowGuideState');
        emit(ShowGuideState());
        return;
      }

      // 非首次启动，检查认证状态
      Logger.info('SplashBloc', '非首次启动，开始检查认证状态');
      await _checkAuthStatus(emit);
    } catch (e) {
      Logger.error('SplashBloc', '继续启动流程失败：$e');
      // 如果出错，直接跳转到登录页
      emit(UnauthenticatedState());
    }
  }

  /// 处理用户同意协议事件
  Future<void> _onAcceptUserAgreement(
    AcceptUserAgreementEvent event,
    Emitter<SplashState> emit,
  ) async {
    try {
      Logger.info('SplashBloc', '开始处理用户同意协议事件');

      // 保存用户同意协议的状态
      await _localStorage.setBool(AppConstants.userAgreementAcceptedKey, true);
      Logger.info('SplashBloc', '用户协议状态已保存');

      // 继续启动流程
      Logger.info('SplashBloc', '开始继续启动流程');
      await _continueAfterAgreement(emit);
      Logger.info('SplashBloc', '启动流程继续完成');
    } catch (e) {
      Logger.error('SplashBloc', '处理用户同意协议失败：$e');
      emit(InitializationFailureState(e.toString()));
    }
  }

  /// 处理用户拒绝协议事件
  void _onRejectUserAgreement(
    RejectUserAgreementEvent event,
    Emitter<SplashState> emit,
  ) {
    Logger.info('SplashBloc', '用户拒绝协议，应用将退出');
    emit(UserAgreementRejectedState());
  }

  /// 检查认证状态
  /// 1. 检查 token 是否存在
  /// 2. 检查用户类型是否存在
  /// 3. 检查用户信息是否存在
  Future<void> _checkAuthStatus(Emitter<SplashState> emit) async {
    try {
      // 首先检查 token 是否存在
      final token = _localStorage.getString(AppConstants.tokenKey);
      Logger.debug('_checkAuthStatus','token: $token');
      if (token == null || token.isEmpty) {
        emit(UnauthenticatedState());
        return;
      }

      // 检查用户是否完成了角色认证
      final userType = _localStorage.getString(AppConstants.userTypeKey);
      Logger.debug('_checkAuthStatus','userType: $userType');
      // 如果用户类型不存在或小于 0（未认证），则导航到登录页面
      if (userType == null) {
        emit(UnauthenticatedState());
        return;
      }

      // 然后检查用户信息是否存在
      final result = await _getCurrentUserUseCase();
      Logger.debug('_checkAuthStatus','result: $result');
      return result.fold(
        (failure) => emit(UnauthenticatedState()),
        (user) {
          if (user != null) {
            emit(AuthenticatedState(userType: userType));
          } else {
            emit(UnauthenticatedState());
          }
        },
      );
    } on Exception {
      emit(UnauthenticatedState());
    }
  }
}
